Stack trace:
Frame         Function      Args
0007FFFF9DA0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CA0) msys-2.0.dll+0x2118E
0007FFFF9DA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9DA0  0002100469F2 (00021028DF99, 0007FFFF9C58, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DA0  00021006A545 (0007FFFF9DB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9DB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBE2AC0000 ntdll.dll
7FFBE1E30000 KERNEL32.DLL
7FFBE0470000 KERNELBASE.dll
7FFBE27B0000 USER32.dll
7FFBE02C0000 win32u.dll
7FFBE0A70000 GDI32.dll
7FFBDFCD0000 gdi32full.dll
7FFBDFF60000 msvcp_win.dll
7FFBDFE10000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBE19C0000 advapi32.dll
7FFBE1D30000 msvcrt.dll
7FFBE0930000 sechost.dll
7FFBE2650000 RPCRT4.dll
7FFBDF210000 CRYPTBASE.DLL
7FFBE0010000 bcryptPrimitives.dll
7FFBE2770000 IMM32.DLL
